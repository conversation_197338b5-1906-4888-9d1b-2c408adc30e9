# دليل اختبار إضافة Agent Zero

## خطوات الاختبار السريع

### 1. التحضير
```bash
cd vscode-extension
npm run copy-assets  # نسخ ملفات webui
npm run compile      # بناء الإضافة
```

### 2. تشغيل الإضافة
1. اضغط `F5` في VS Code لفتح نافذة Extension Development Host
2. في النافذة الجديدة، اضغط `Ctrl+Shift+P`
3. ابحث عن "Agent Zero: Show Chat"
4. أو انقر على أيقونة Agent Zero في الشريط الجانبي

### 3. فحص الواجهة
يجب أن ترى:
- ✅ واجهة Agent Zero الكاملة في الشريط الجانبي
- ✅ جميع الأزرار والقوائم
- ✅ الأنماط والألوان الصحيحة
- ✅ الأيقونات والصور

### 4. اختب<PERSON>ر الوظائف الأساسية
- [ ] كتابة رسالة في صندوق النص
- [ ] النقر على زر الإرسال
- [ ] فتح الإعدادات
- [ ] تبديل الوضع المظلم/الفاتح
- [ ] فتح متصفح الملفات

### 5. فحص الأخطاء
إذا لم تعمل الواجهة:

#### أ. فتح أدوات المطور
1. في نافذة Extension Development Host
2. اضغط `Ctrl+Shift+P`
3. ابحث عن "Developer: Toggle Developer Tools"
4. انتقل إلى تبويب Console

#### ب. الأخطاء الشائعة وحلولها

**خطأ: "WebUI index.html not found"**
```bash
# الحل
cd vscode-extension
npm run copy-assets
npm run compile
```

**خطأ: "Failed to load resource"**
- تحقق من وجود الملفات في `media/`
- تأكد من تشغيل `copy-assets`

**خطأ: "Content Security Policy"**
- هذا طبيعي، الكود يتعامل معه تلقائياً
- تأكد من وجود nonce في السكريبتات

**واجهة فارغة أو بيضاء**
- افتح Console وابحث عن أخطاء JavaScript
- تحقق من تحميل ملفات CSS

### 6. اختبار متقدم

#### أ. اختبار الاتصال
```javascript
// في Console، اختبر وجود الكائنات الأساسية
console.log(window.Alpine);  // يجب أن يكون موجود
console.log(window.io);      // Socket.IO
```

#### ب. اختبار الرسائل
```javascript
// اختبار إرسال رسالة من VS Code إلى الواجهة
// (هذا للمطورين المتقدمين)
```

### 7. اختبار الأداء
- [ ] سرعة تحميل الواجهة
- [ ] استجابة الأزرار
- [ ] سلاسة التمرير
- [ ] استهلاك الذاكرة

### 8. اختبار التوافق
- [ ] Windows
- [ ] macOS  
- [ ] Linux
- [ ] VS Code versions مختلفة

## نتائج الاختبار المتوقعة

### ✅ نجح الاختبار إذا:
1. الواجهة تظهر بشكل صحيح
2. جميع الأنماط محملة
3. الأزرار تستجيب للنقر
4. لا توجد أخطاء في Console
5. الأيقونات والصور تظهر

### ❌ فشل الاختبار إذا:
1. واجهة فارغة أو بيضاء
2. أخطاء في Console
3. ملفات CSS/JS لا تحمل
4. الأزرار لا تعمل

## تقرير الأخطاء

إذا واجهت مشاكل، اجمع المعلومات التالية:

### معلومات النظام
- نظام التشغيل: ___________
- إصدار VS Code: ___________
- إصدار Node.js: ___________

### لقطات الشاشة
- [ ] الواجهة كما تظهر
- [ ] رسائل الخطأ في Console
- [ ] محتويات مجلد media/

### سجل الأخطاء
```
نسخ رسائل الخطأ من Console هنا
```

### خطوات إعادة الإنتاج
1. ___________
2. ___________
3. ___________

## نصائح للمطورين

### تطوير سريع
```bash
# مراقبة التغييرات
npm run watch

# نسخ الملفات عند التحديث
npm run copy-assets && npm run compile
```

### تشخيص المشاكل
1. تحقق من `out/extension.js` للتأكد من البناء
2. تحقق من `media/` للتأكد من نسخ الملفات
3. استخدم `console.log` في ChatWebviewProvider.ts

### تحسين الأداء
- ضغط ملفات CSS/JS
- تحميل lazy للمكونات الكبيرة
- تحسين صور SVG

---

**ملاحظة**: هذا الدليل يفترض أن مجلد `webui` موجود بجوار مجلد `vscode-extension`. إذا كان في مكان آخر، عدّل المسار في `copy-webui-assets.js`.
