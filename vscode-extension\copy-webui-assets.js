const fs = require('fs');
const path = require('path');

// مسارات المجلدات
const webuiPath = path.join(__dirname, '..', 'webui');
const mediaPath = path.join(__dirname, 'media');

// دالة لنسخ ملف
function copyFile(src, dest) {
    try {
        const destDir = path.dirname(dest);
        if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
        }
        fs.copyFileSync(src, dest);
        console.log(`✓ Copied: ${path.relative(__dirname, src)} → ${path.relative(__dirname, dest)}`);
    } catch (error) {
        console.error(`✗ Failed to copy ${src}:`, error.message);
    }
}

// دالة لنسخ مجلد بالكامل
function copyDirectory(src, dest) {
    if (!fs.existsSync(src)) {
        console.warn(`⚠ Source directory not found: ${src}`);
        return;
    }

    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    for (const item of items) {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            copyFile(srcPath, destPath);
        }
    }
}

console.log('🚀 Starting WebUI assets copy...\n');

// نسخ الملفات الأساسية
const filesToCopy = [
    { src: 'index.html', dest: 'index.html' },
    { src: 'index.css', dest: 'index.css' },
    { src: 'index.js', dest: 'index.js' }
];

for (const file of filesToCopy) {
    const srcPath = path.join(webuiPath, file.src);
    const destPath = path.join(mediaPath, file.dest);
    
    if (fs.existsSync(srcPath)) {
        copyFile(srcPath, destPath);
    } else {
        console.warn(`⚠ File not found: ${srcPath}`);
    }
}

// نسخ مجلدات CSS و JS
const directoriesToCopy = [
    { src: 'css', dest: 'css' },
    { src: 'js', dest: 'js' },
    { src: 'public', dest: 'public' },
    { src: 'vendor', dest: 'vendor' },
    { src: 'components', dest: 'components' }
];

for (const dir of directoriesToCopy) {
    const srcPath = path.join(webuiPath, dir.src);
    const destPath = path.join(mediaPath, dir.dest);
    
    console.log(`\n📁 Copying directory: ${dir.src}/`);
    copyDirectory(srcPath, destPath);
}

console.log('\n✅ WebUI assets copy completed!');
console.log('\n📝 Note: Run this script whenever you update the webui files.');
